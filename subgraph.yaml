specVersion: 1.0.0
features:
  - fullTextSearch
indexerHints:
  prune: auto
schema:
  file: ./updraft-schemas/schema.graphql
dataSources:
  - kind: ethereum
    name: Updraft
    network: arbitrum-sepolia
    source:
      address: "******************************************"
      abi: Updraft
      startBlock: 152303148
    mapping:
      kind: ethereum/events
      apiVersion: 0.0.7
      language: wasm/assemblyscript
      entities:
        - Idea
        - User
        - Solution
        - TagCount
      abis:
        - name: Updraft
          file: ./abis/Updraft.json
      eventHandlers:
        - event: IdeaCreated(indexed address,indexed address,uint256,uint256,bytes)
          handler: handleIdeaCreated
        - event: ProfileUpdated(indexed address,bytes)
          handler: handleProfileUpdated
        - event: SolutionCreated(indexed address,indexed address,indexed
            address,address,uint256,uint256,uint256,uint256,bytes)
          handler: handleSolutionCreated
      file: ./src/updraft.ts
templates:
  - kind: ethereum
    name: Idea
    network: arbitrum-sepolia
    source:
      abi: Idea
    mapping:
      kind: ethereum/events
      apiVersion: 0.0.7
      language: wasm/assemblyscript
      entities:
        - IdeaContribution
      abis:
        - name: Idea
          file: ./abis/Idea.json
      eventHandlers:
        - event: Contributed(indexed address,uint256,uint256,uint256,uint256)
          handler: handleContributed
        - event: PositionTransferred(indexed address,indexed address,uint256,uint256,uint256)
          handler: handlePositionTransferred
        - event: Split(indexed address,uint256,uint256,uint256,uint256,uint256)
          handler: handleSplit
        - event: Withdrew(indexed address,uint256,uint256,uint256,uint256,uint256)
          handler: handleWithdrew
      file: ./src/idea.ts
  - kind: ethereum
    name: Solution
    network: arbitrum-sepolia
    source:
      abi: Solution
    mapping:
      kind: ethereum/events
      apiVersion: 0.0.7
      language: wasm/assemblyscript
      entities:
        - SolutionContribution
        - Stake
      abis:
        - name: Solution
          file: ./abis/Solution.json
      eventHandlers:
        - event: Contributed(indexed address,uint256,uint256,uint256,uint256)
          handler: handleContributed
        - event: GoalExtended(uint256,uint256)
          handler: handleGoalExtended
        - event: PositionTransferred(indexed address,indexed address,uint256,uint256,uint256)
          handler: handlePositionTransferred
        - event: Refunded(indexed address,uint256,uint256,uint256)
          handler: handleRefunded
        - event: SolutionUpdated(bytes)
          handler: handleSolutionUpdated
        - event: Split(indexed address,uint256,uint256,uint256,uint256,uint256)
          handler: handleSplit
        - event: StakeUpdated(indexed address,uint256,uint256)
          handler: handleStakeUpdated
      file: ./src/solution.ts