{"compilerOptions": {"target": "ES2022", "experimentalDecorators": true, "useDefineForClassFields": false, "module": "ESNext", "lib": ["ES2022", "DOM", "DOM.Iterable"], "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "allowSyntheticDefaultImports": true, "isolatedModules": true, "moduleDetection": "force", "resolveJsonModule": true, "noEmit": true, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "sourceMap": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@components/idea/*": ["src/features/idea/components/*"], "@components/solution/*": ["src/features/solution/components/*"], "@components/common/*": ["src/features/common/components/*"], "@components/layout/*": ["src/features/layout/components/*"], "@components/navigation/*": ["src/features/navigation/components/*"], "@components/home/<USER>": ["src/features/pages/home/<USER>/*"], "@components/tags/*": ["src/features/tags/components/*"], "@styles/idea/*": ["src/features/idea/styles/*"], "@styles/solution/*": ["src/features/solution/styles/*"], "@styles/common/*": ["src/features/common/styles/*"], "@styles/layout/*": ["src/features/layout/styles/*"], "@styles/navigation/*": ["src/features/navigation/styles/*"], "@state/common": ["src/features/common/state"], "@state/idea": ["src/features/idea/state"], "@state/layout": ["src/features/layout/state"], "@state/navigation": ["src/features/navigation/state"], "@state/solution": ["src/features/solution/state"], "@state/tags": ["src/features/tags/state"], "@state/user": ["src/features/user/state"], "@state/common/*": ["src/features/common/state/*"], "@state/idea/*": ["src/features/idea/state/*"], "@state/layout/*": ["src/features/layout/state/*"], "@state/navigation/*": ["src/features/navigation/state/*"], "@state/solution/*": ["src/features/solution/state/*"], "@state/tags/*": ["src/features/tags/state/*"], "@state/user/*": ["src/features/user/state/*"], "@state/home/<USER>": ["src/features/pages/home/<USER>/*"], "@/types/user/*": ["src/features/user/types/*"], "@icons/common/*": ["src/features/common/assets/icons/*"], "@icons/idea/*": ["src/features/idea/assets/icons/*"], "@icons/navigation/*": ["src/features/navigation/assets/icons/*"], "@icons/solution/*": ["src/features/solution/assets/icons/*"], "@icons/user/*": ["src/features/user/assets/icons/*"], "@utils/common/*": ["src/features/common/utils/*"], "@utils/create-solution/*": ["src/features/pages/create-solution/utils/*"], "@utils/home/<USER>": ["src/features/pages/home/<USER>/*"], "@utils/idea/*": ["src/features/idea/utils/*"], "@utils/solution/*": ["src/features/solution/utils/*"], "@utils/user/*": ["src/features/user/utils/*"], "@images/*": ["src/features/common/assets/images/*"], "@pages/*": ["src/features/pages/*"], "@layout/*": ["src/features/layout/components/*"], "@styles/*": ["src/features/common/styles/*"], "@utils/*": ["src/features/common/utils/*"], "@contracts/*": ["src/lib/contracts/*"], "@schemas/*": ["updraft-schemas/json-schemas/*"], "@gql": [".graphclient"]}}, "include": ["src"]}