[{"inputs": [{"internalType": "contract IERC20", "name": "feeToken_", "type": "address"}, {"internalType": "uint256", "name": "minFee_", "type": "uint256"}, {"internalType": "uint256", "name": "percentFee_", "type": "uint256"}, {"internalType": "uint256", "name": "cycleLength_", "type": "uint256"}, {"internalType": "uint256", "name": "accrualRate_", "type": "uint256"}, {"internalType": "address", "name": "humanity_", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "SafeERC20FailedOperation", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "contract Idea", "name": "idea", "type": "address"}, {"indexed": true, "internalType": "address", "name": "creator", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "contributorFee", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "contribution", "type": "uint256"}, {"indexed": false, "internalType": "bytes", "name": "data", "type": "bytes"}], "name": "IdeaCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": false, "internalType": "bytes", "name": "data", "type": "bytes"}], "name": "ProfileUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "contract Solution", "name": "solution", "type": "address"}, {"indexed": true, "internalType": "address", "name": "creator", "type": "address"}, {"indexed": true, "internalType": "address", "name": "idea", "type": "address"}, {"indexed": false, "internalType": "contract IERC20", "name": "fundingToken", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "stake", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "goal", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "deadline", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "contributorFee", "type": "uint256"}, {"indexed": false, "internalType": "bytes", "name": "data", "type": "bytes"}], "name": "SolutionCreated", "type": "event"}, {"inputs": [], "name": "accrualRate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "contributorFee", "type": "uint256"}, {"internalType": "uint256", "name": "contribution", "type": "uint256"}, {"internalType": "bytes", "name": "ideaData", "type": "bytes"}], "name": "createIdea", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "contributorFee", "type": "uint256"}, {"internalType": "uint256", "name": "contribution", "type": "uint256"}, {"internalType": "bytes", "name": "ideaData", "type": "bytes"}, {"internalType": "bytes", "name": "profileData", "type": "bytes"}], "name": "createIdeaWithProfile", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "idea", "type": "address"}, {"internalType": "contract IERC20", "name": "fundingToken", "type": "address"}, {"internalType": "uint256", "name": "stake", "type": "uint256"}, {"internalType": "uint256", "name": "goal", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint256", "name": "contributorFee", "type": "uint256"}, {"internalType": "bytes", "name": "solutionData", "type": "bytes"}], "name": "createSolution", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "idea", "type": "address"}, {"internalType": "contract IERC20", "name": "fundingToken", "type": "address"}, {"internalType": "uint256", "name": "stake", "type": "uint256"}, {"internalType": "uint256", "name": "goal", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint256", "name": "contributorFee", "type": "uint256"}, {"internalType": "bytes", "name": "solutionData", "type": "bytes"}, {"internalType": "bytes", "name": "profileData", "type": "bytes"}], "name": "createSolutionWithProfile", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "cycle<PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "feeToken", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "humanity", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "minFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "percentFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "percentScale", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "rate", "type": "uint256"}], "name": "setAccrualRate", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "length", "type": "uint256"}], "name": "setCycle<PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "contract IERC20", "name": "token", "type": "address"}], "name": "setFeeToken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "humanity_", "type": "address"}], "name": "setHumanity", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "fee", "type": "uint256"}], "name": "setMinFee", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "fee", "type": "uint256"}], "name": "setPercentFee", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "profileData", "type": "bytes"}], "name": "updateProfile", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]