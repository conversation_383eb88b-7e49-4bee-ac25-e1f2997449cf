{"name": "updraft", "license": "MIT", "scripts": {"codegen": "graph codegen", "build": "graph build", "deploy": "graph deploy --node https://api.studio.thegraph.com/deploy/ --ipfs=https://ipfs.network.thegraph.com updraft", "create-local": "graph create --node http://localhost:8020/ updraft", "remove-local": "graph remove --node http://localhost:8020/ updraft", "deploy-local": "graph deploy --node http://localhost:8020/ --ipfs http://localhost:5001 updraft", "test": "graph test"}, "dependencies": {"@graphprotocol/graph-cli": "0.97.0", "@graphprotocol/graph-ts": "0.38.0"}, "devDependencies": {"matchstick-as": "0.6.0"}}