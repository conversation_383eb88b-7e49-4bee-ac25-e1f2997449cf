import { LitElement, css } from 'lit';
import { customElement } from 'lit/decorators.js';
import { SignalWatcher, html } from '@lit-labs/signals';

import '@shoelace-style/shoelace/dist/components/icon/icon.js';
import '@shoelace-style/shoelace/dist/components/icon-button/icon-button.js';

import listIcon from '@icons/navigation/list.svg';

import updraftLogo from '@images/updraft-logo-46.png';
import '@components/navigation/user-menu';

import { topBarContent, toggleLeftSidebar } from '@state/layout';

@customElement('top-bar')
export class TopBar extends SignalWatcher(LitElement) {
  static styles = css`
    :host {
      background: var(--subtle-background);
      color: var(--main-foreground);
      display: flex;
      height: 64px;
      padding: 0 24px;
      justify-content: space-between;
      align-items: center;
      gap: 24px;
      overflow: clip;
    }
    a {
      line-height: 0;
    }
    .logo-button img {
      border-radius: 50%;
    }
    .menu-button {
      display: none;
    }
    sl-icon-button {
      color: var(--main-foreground);
      font-size: 1.5rem;
    }
    .content {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 2rem;
      overflow: clip;
      min-width: 0;
    }
    .content div {
      display: flex;
      flex: 1;
      align-items: center;
    }
    page-heading a {
      font-size: 1rem;
    }
    @media (max-width: 860px) {
      :host {
        padding: 0 1rem;
        gap: 1rem;
      }
      .content {
        justify-content: flex-start;
        gap: 1rem;
      }
    }
    @media (max-width: 768px) {
      .menu-button {
        display: block;
      }
      .logo-button {
        display: none;
      }
      :host {
        padding: 0 0.5rem;
        gap: 0.5rem;
      }
      .content {
        justify-content: flex-start;
        gap: 0.5rem;
      }
    }
  `;

  render() {
    return html`
      <sl-icon-button
        class="menu-button"
        src="${listIcon}"
        label="Menu"
        @click=${toggleLeftSidebar}
      ></sl-icon-button>
      <a href="/" title="Updraft Home" class="logo-button">
        <img src="${updraftLogo}" alt="Updraft logo" />
      </a>
      <div class="content">${topBarContent.get()}</div>
      <user-menu></user-menu>
    `;
  }
}

declare global {
  interface HTMLElementTagNameMap {
    'top-bar': TopBar;
  }
}
