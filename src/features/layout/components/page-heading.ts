import { LitElement, html, css } from 'lit';
import { customElement } from 'lit/decorators.js';

@customElement('page-heading')
export class PageHeading extends LitElement {
  static styles = css`
    :host {
      display: inline-block;
      margin-right: auto;
      font-size: 2.25rem;
      font-weight: 500;
      margin-left: clamp(0px, calc((100vw - 670px) * 0.5), 200px);
      color: var(--main-foreground);
      line-height: 1.2;
    }

    /* Responsive adjustments for smaller screens */
    @media (max-width: 768px) {
      :host {
        font-size: 1.75rem;
        margin-left: 1rem;
      }
    }

    @media (max-width: 480px) {
      :host {
        font-size: 1.5rem;
        margin-left: 0.5rem;
      }
    }
  `;

  render() {
    return html` <slot></slot>`;
  }
}

declare global {
  interface HTMLElementTagNameMap {
    'page-heading': PageHeading;
  }
}
