.sl-theme-light {
  --sl-color-primary-50: rgb(247 250 251);
  --sl-color-primary-100: rgb(228 238 243);
  --sl-color-primary-200: rgb(208 225 235);
  --sl-color-primary-300: rgb(185 210 225);
  --sl-color-primary-400: rgb(155 191 212);
  --sl-color-primary-500: rgb(116 166 195);
  --sl-color-primary-600: rgb(74 139 177);
  --sl-color-primary-700: rgb(36 114 160);
  --sl-color-primary-800: rgb(14 94 140);
  --sl-color-primary-900: rgb(10 67 100);
  --sl-color-primary-950: rgb(6 41 62);
  --subtle-background: #dff5fc;
  --dialog-border: var(--sl-color-neutral-100);
  --sidebar-toggle-background: var(--main-background);
}

.sl-theme-dark {
  --sl-color-primary-50: rgb(6 41 62);
  --sl-color-primary-100: rgb(10 67 100);
  --sl-color-primary-200: rgb(14 94 140);
  --sl-color-primary-300: rgb(36 114 160);
  --sl-color-primary-400: rgb(74 139 177);
  --sl-color-primary-500: rgb(116 166 195);
  --sl-color-primary-600: rgb(155 191 212);
  --sl-color-primary-700: rgb(185 210 225);
  --sl-color-primary-800: rgb(208 225 235);
  --sl-color-primary-900: rgb(228 238 243);
  --sl-color-primary-950: rgb(247 250 251);
  --subtle-background: var(--sl-color-primary-100);
  --dialog-border: var(--sl-color-neutral-400);
}

/* Use var(--sl-color-{name}-{n}) to have inverted colors for dark and light themes.
   See https://shoelace.style/tokens/color for built-in theme and palette names. */
:root, .sl-theme-dark, .sl-theme-light {
  --sl-input-border-color: var(--sl-color-primary-300);
  --sl-input-focus-ring-color: var(--sl-color-primary-300);
  --main-background: var(--sl-color-neutral-0);
  --accent: var(--sl-color-primary-800);
  --link: var(--sl-color-primary-500);
  --main-foreground: var(--sl-color-neutral-800);
  --subtle-text: var(--sl-color-neutral-600);
  --no-results: var(--sl-color-neutral-500);
  --control-background: var(--sl-color-sky-200);
  --section-heading: var(--sl-color-neutral-500);
  --layout-divider: var(--sl-color-neutral-200);
  --danger: var(--sl-color-danger-600);
  --success: var(--sl-color-success-600);
  --attention: var(--sl-color-warning-600);
  --border-default: var(--sl-color-neutral-200);
}

/* Tab group specific styling */
sl-tab-group::part(base) {
  --indicator-color: var(--accent);
  --track-color: var(--border-default);
}

sl-tab::part(base) {
  color: var(--main-foreground);
}

sl-tab[active]::part(base) {
  color: var(--accent);
  font-weight: 600;
}
