type User @entity(immutable: false) {
  id: Bytes! # address
  profile: Bytes
}

type Idea @entity(immutable: false) {
  id: Bytes! # address
  creator: User! @index
  startTime: BigInt! @index # uint256
  shares: BigInt! @index # uint256
  name: String
  description: String
  tags: [String!] @index
  funderReward: BigInt! #uint256
}

type TagCount @entity(immutable: false) {
  id: String!
  count: Int! @index
}

type _Schema_ @fulltext(
  name: "ideaSearch"
  language: en
  algorithm: rank
  include: [{ entity: "Idea", fields: [{ name: "name" }, { name: "description" }] }]
)

type IdeaContribution @entity(immutable: false) {
  id: Bytes! # hash of idea, funder, and positionIndex
  idea: Idea!
  funder: User! @index
  positionIndex: BigInt! # uint256
  contribution: BigInt! # uint256
  createdTime: BigInt! @index # uint256
}

type Solution @entity(immutable: false) {
  id: Bytes! # address
  idea: Idea! @index
  drafter: User! @index
  fundingToken: Bytes! @index # address
  startTime: BigInt! @index # uint256
  deadline: BigInt! @index # uint256
  modifiedTime: BigInt! @index # uint256
  shares: BigInt! @index # uint256
  tokensContributed: BigInt! # uint256
  fundingGoal: BigInt! #uint256
  sweetness: BigInt! @index #uint256
  stake: BigInt! @index # uint256
  info: Bytes! # JSON blob 'data'
  funderReward: BigInt! #uint256
}

type SolutionContribution @entity(immutable: false) {
  id: Bytes! # hash of solution, funder, and positionIndex
  solution: Solution!
  funder: User! @index
  positionIndex: BigInt! # uint256
  contribution: BigInt! # uint256
  refunded: Boolean!
  createdTime: BigInt! @index # uint256
}