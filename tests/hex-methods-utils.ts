import { Bytes, BigInt } from "@graphprotocol/graph-ts"

// A struct to hold test case data
export class HexTestCase {
  id: Bytes
  ideaAddress: Bytes
  userAddress: Bytes
  positionIndex: BigInt
  
  constructor(id: Bytes, ideaAddress: Bytes, userAddress: Bytes, positionIndex: BigInt) {
    this.id = id
    this.ideaAddress = ideaAddress
    this.userAddress = userAddress
    this.positionIndex = positionIndex
  }
}

// Create a test case with realistic data
export function createHexTestCase(): HexTestCase {
  // Sample Ethereum addresses
  const ideaAddress = Bytes.fromHexString('******************************************') as Bytes
  const userAddress = Bytes.fromHexString('******************************************') as Bytes
  const positionIndex = BigInt.fromI32(42)
  
  // Create a sample ID (could be any bytes)
  const id = Bytes.fromHexString('0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890') as Bytes
  
  return new HexTestCase(id, ideaAddress, userAddress, positionIndex)
}
